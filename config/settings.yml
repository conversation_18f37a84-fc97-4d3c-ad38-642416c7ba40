settings:
  application:
    # dev开发环境 test测试环境 prod线上环境
    mode: dev
    # 服务器ip，默认使用 0.0.0.0
    host: 0.0.0.0
    # 服务名称
    name: testApp
    # 端口号
    port: 8000 # 服务端口号
    readtimeout: 1
    writertimeout: 2
    # 数据权限功能开关
    enabledp: false
  logger:
    # 日志存放路径
    path: temp/logs
    # 日志输出，file：文件，default：命令行，其他：命令行
    stdout: '' #控制台日志，启用后，不输出到文件
    # 日志等级, trace, debug, info, warn, error, fatal
    level: trace
    # 数据库日志开关
    enableddb: true
  jwt:
    # token 密钥，生产环境时及的修改
    secret: go-admin
    # token 过期时间 单位：秒
    timeout: 3600
  zookeeper:
    addr:
      - *************:50010
    #连接超时时间
    timeout: 3000

  database:
    # 数据库类型 mysql, sqlite3, postgres, sqlserver
    # sqlserver: sqlserver://用户名:密码@地址?database=数据库名
    driver: mysql
    # 数据库连接字符串 mysql 缺省信息 charset=utf8&parseTime=True&loc=Local&timeout=1000ms
    source: root:fancydb2024#@tcp(192.168.1.52)/fancyplt?charset=utf8mb4&parseTime=True&loc=Local&timeout=1000ms&collation=utf8mb4_general_ci


#  # 外部数据库
#  databaseExt:
#    - name: configure_db
#      driver: mysql
#      source: root:diyal520$@tcp(**************)/db_base_configure?charset=utf8&parseTime=True&loc=Local&timeout=1000ms

  databaseCk:
    # ClickHouse配置
    driver: clickhouse
    # 数据库连接字符串 clickhouse 缺省信息 charset=utf8&parseTime=True&loc=Local&timeout=1000ms
    source: tcp://*************:28004?database=mtbigdata&username=root&password=XwCoKBgV&read_timeout=10&write_timeout=20

  #  databases:
#    'locaohost:8000':
#      driver: mysql
#        # 数据库连接字符串 mysql 缺省信息 charset=utf8&parseTime=True&loc=Local&timeout=1000ms
#        source: user:password@tcp(127.0.0.1:3306)/dbname?charset=utf8&parseTime=True&loc=Local&timeout=1000ms
#      registers:
#        - sources:
#            - user:password@tcp(127.0.0.1:3306)/dbname?charset=utf8&parseTime=True&loc=Local&timeout=1000ms
  gen:
    # 代码生成读取的数据库名称
    dbname: fancypltgen
    # 代码生成是使用前端代码存放位置，需要指定到src文件夹，相对路径
    frontpath: ../tc_platform_ui/src
  extend: # 扩展项使用说明
    demo:
      name: data
    GMInfo:
      GMKey: "SHJKDSA^&@!HJ!@GHJVASCSIUJ*("
      GMList:
        - addr: "http://************:5980"
          env: "develop"
        - addr: "http://**************:5980"
          env: "test"
        - addr: "http://*************:5980"
          env: "beta"
        - addr: "http://**************:5980"
          env: "audit"
        - addr: "http://192.168.0.87:20004"
          env: "release"
    BitLy:
      UserName: <EMAIL>
      Password: liuyalong199027
      ClientId: bd41b1197c406ff23beca5d019be342df2b2239f
      ClientSecret: 4b5ec1ecbb6f553fa49a9435e13b449827ca0cbb
  cache:
#    redis:
#      addr: 127.0.0.1:6379
#      password: xxxxxx
#      db: 2
    # key存在即可
    memory: ''
  queue:
    memory:
      poolSize: 200
#    redis:
#      addr: 127.0.0.1:6379
#      password: xxxxxx
#      producer:
#        streamMaxLength: 100
#        approximateMaxLength: true
#      consumer:
#        visibilityTimeout: 60
#        bufferSize: 100
#        concurrency: 10
#        blockingTimeout: 5
#        reclaimInterval: 1
  locker:
    redis:
  gaea:
    homeAddr: http://*************:24004/home

  # S3
  s3:
    bucketKey: clientsrv
    region: us-east-2
    accessKey: ********************
    secretKey: DrSbUt9mOiLDeFnTb+Cscz/qc7GeGFDd/q258S0F
    remoteBucketHost:

  # CloudFront
  cloudfront:
    distributionId: E2FUE72MJJ0YT8
    enabled: true
