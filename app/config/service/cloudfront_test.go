package service

import (
	"go-admin/core/sdk/config"
	"testing"
	"time"
)

func TestInvalidateCloudFront(t *testing.T) {
	// 设置测试配置
	config.CloudFrontConfig.DistributionID = "E2FUE72MJJ0YT8"
	config.CloudFrontConfig.Enabled = true

	// accessKey: ********************
	// secretKey: DrSbUt9mOiLDeFnTb+Cscz/qc7GeGFDd/q258S0F

	// 测试路径
	testPath := "/*"

	// 调用函数（异步执行，这里只测试不会panic）
	InvalidateCloudFront(testPath)
	time.Sleep()
}
