package service

import (
	"fmt"
	"go-admin/core/sdk/config"
)

// DebugAWSConfig 调试AWS配置
func DebugAWSConfig() {
	fmt.Printf("=== AWS Configuration Debug ===\n")
	fmt.Printf("S3 Config:\n")
	fmt.Printf("  BucketKey: %s\n", config.S3Config.BucketKey)
	fmt.Printf("  Region: %s\n", config.S3Config.Region)
	fmt.Printf("  AccessKey: %s\n", maskSecretKey(config.S3Config.AccessKey))
	fmt.Printf("  SecretKey: %s\n", maskS<PERSON><PERSON><PERSON>ey(config.S3Config.SecretKey))
	fmt.Printf("  RemoteBucketHost: %s\n", config.S3Config.RemoteBucketHost)
	
	fmt.Printf("\nCloudFront Config:\n")
	fmt.Printf("  DistributionID: %s\n", config.CloudFrontConfig.DistributionID)
	fmt.Printf("  Enabled: %v\n", config.CloudFrontConfig.Enabled)
	fmt.Printf("================================\n")
}
