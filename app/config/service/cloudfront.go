package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/cloudfront"
	"github.com/aws/aws-sdk-go-v2/service/cloudfront/types"
	"go-admin/core/logger"
	"go-admin/core/sdk/config"
	"go-admin/core/sdk/pkg/awssdk"
)

// InvalidateCloudFront 失效CloudFront缓存
func InvalidateCloudFront(path string) {
	// 检查是否启用
	if !config.CloudFrontConfig.Enabled || config.CloudFrontConfig.DistributionID == "" {
		logger.Info("CloudFront invalidation is disabled, skipping cache invalidation")
		return
	}

	// 异步执行，不阻塞主流程
	go func() {
		ctx := context.Background()

		// 检查S3配置是否有效
		if config.S3Config.AccessKey == "" || config.S3Config.SecretKey == "" {
			logger.Errorf("AWS credentials are empty. AccessKey: '%s', SecretKey: '%s'",
				config.S3Config.AccessKey,
				maskSecretKey(config.S3Config.SecretKey))
			return
		}

		// 加载AWS配置
		cfg, err := awssdk.LoadS3Cfg()
		if err != nil {
			logger.Errorf("Failed to load AWS config for CloudFront: %v", err)
			return
		}

		// 创建CloudFront客户端
		client := cloudfront.NewFromConfig(cfg)

		// 生成唯一的调用者引用
		callerReference := fmt.Sprintf("go-admin-%d", time.Now().UnixNano())

		// 构建失效请求
		invalidationBatch := &types.InvalidationBatch{
			CallerReference: aws.String(callerReference),
			Paths: &types.Paths{
				Quantity: aws.Int32(1),
				Items:    []string{path},
			},
		}

		input := &cloudfront.CreateInvalidationInput{
			DistributionId:    aws.String(config.CloudFrontConfig.DistributionID),
			InvalidationBatch: invalidationBatch,
		}

		// 执行失效请求
		result, err := client.CreateInvalidation(ctx, input)
		if err != nil {
			logger.Errorf("CloudFront invalidation failed: %v", err)
			return
		}

		logger.Infof("CloudFront invalidation created: ID=%s, Status=%s, Path=%s",
			aws.ToString(result.Invalidation.Id),
			aws.ToString(result.Invalidation.Status),
			path)
	}()
}

// maskSecretKey 遮盖密钥用于日志输出
func maskSecretKey(key string) string {
	if len(key) <= 8 {
		return strings.Repeat("*", len(key))
	}
	return key[:4] + strings.Repeat("*", len(key)-8) + key[len(key)-4:]
}
