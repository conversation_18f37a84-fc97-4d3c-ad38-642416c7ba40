# CloudFront缓存失效功能

## 概述

简单的CloudFront缓存失效功能，在S3文件上传完成后自动失效整个目录的缓存。

## 配置

在 `config/settings.yml` 中添加：

```yaml
# CloudFront
cloudfront:
  distributionId: E1234567890ABC  # 替换为实际的CloudFront Distribution ID
  enabled: true                  # 是否启用CloudFront失效功能
```

## 使用

### 自动集成

已自动集成到 `Deploy2S3` 函数中，上传完成后会自动调用：

```go
// 失效路径格式：
// 生产环境: /{productID}/{channelID}/*
// 其他环境: /{prefix}/{envType}/{productID}/{channelID}/*
InvalidateCloudFront(invalidationPath)
```

### 手动调用

```go
import "go-admin/app/config/service"

// 失效整个目录
service.InvalidateCloudFront("/123/456/*")
```

## 功能特性

- ✅ 异步执行，不阻塞主流程
- ✅ 自动生成唯一的调用者引用
- ✅ 完整的错误日志
- ✅ 配置开关控制

## 日志示例

```
INFO: CloudFront invalidation created: ID=I1234567890ABC, Status=InProgress, Path=/123/456/*
INFO: CloudFront invalidation is disabled, skipping cache invalidation
ERROR: CloudFront invalidation failed: AccessDenied
```

## AWS权限要求

确保AWS凭证具有 `cloudfront:CreateInvalidation` 权限。

### 方法1: 添加CloudFront权限策略

在AWS IAM中为用户添加以下权限策略：

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "cloudfront:CreateInvalidation",
                "cloudfront:GetInvalidation"
            ],
            "Resource": "*"
        }
    ]
}
```

### 方法2: 使用AWS托管策略

直接附加AWS托管策略：`CloudFrontFullAccess` 或创建自定义策略。

### 权限验证

如果看到以下错误，说明权限配置正确，只是缺少CloudFront权限：
```
AccessDenied: User: arn:aws:iam::xxx:user/S3Client is not authorized to perform: cloudfront:CreateInvalidation
```

配置权限后，CloudFront失效功能就能正常工作了。

## 文件结构

- `app/config/service/cloudfront.go` - CloudFront失效函数
- `app/config/service/t_config_deploy.go` - S3部署集成
- `core/sdk/config/s3.go` - 配置结构
